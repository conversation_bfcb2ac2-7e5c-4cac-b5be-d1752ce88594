#!/usr/bin/env python3
"""
用户交易画像数据填充脚本
从position_analysis表生成user_trading_profiles表的数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime, timedelta
from database.duckdb_manager import db_manager
from modules.user_analysis.services.user_behavior_analyzer import UserBehaviorAnalyzer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UserTradingProfilesPopulator:
    """用户交易画像数据填充器"""
    
    def __init__(self):
        self.analyzer = UserBehaviorAnalyzer()
    
    def populate_all_users(self, limit=None):
        """为所有用户生成交易画像数据"""
        try:
            # 获取所有有交易记录的用户
            sql = """
            SELECT DISTINCT member_id, COUNT(*) as trade_count
            FROM position_analysis 
            GROUP BY member_id
            ORDER BY trade_count DESC
            """
            
            if limit:
                sql += f" LIMIT {limit}"
            
            users = db_manager.fetch_all(sql)
            logger.info(f"找到 {len(users)} 个用户需要生成交易画像")
            
            success_count = 0
            for i, user in enumerate(users):
                member_id = user['member_id']
                trade_count = user['trade_count']
                
                try:
                    logger.info(f"处理用户 {i+1}/{len(users)}: {member_id} (交易数: {trade_count})")
                    
                    # 检查是否已存在
                    existing = db_manager.fetch_all(
                        "SELECT member_id FROM user_trading_profiles WHERE member_id = ?",
                        [member_id]
                    )
                    
                    if existing:
                        logger.info(f"用户 {member_id} 的画像已存在，跳过")
                        continue
                    
                    # 生成交易画像
                    profile = self._generate_user_profile(member_id)
                    if profile:
                        self._save_user_profile(profile)
                        success_count += 1
                        logger.info(f"✅ 用户 {member_id} 画像生成成功")
                    else:
                        logger.warning(f"❌ 用户 {member_id} 画像生成失败")
                        
                except Exception as e:
                    logger.error(f"处理用户 {member_id} 失败: {e}")
                    continue
            
            logger.info(f"批量生成完成！成功: {success_count}/{len(users)}")
            
        except Exception as e:
            logger.error(f"批量生成失败: {e}")
            raise
    
    def _generate_user_profile(self, member_id: str):
        """为单个用户生成交易画像"""
        try:
            # 获取用户交易数据
            positions_sql = """
            SELECT * FROM position_analysis 
            WHERE member_id = ?
            ORDER BY open_time
            """
            positions = db_manager.fetch_all(positions_sql, [member_id])
            
            if not positions:
                return None
            
            # 转换为分析器需要的格式
            analysis_result = self.analyzer.analyze_user_behavior(member_id, positions)
            
            # 构建用户画像数据
            profile_data = self._build_profile_data(member_id, analysis_result, positions)
            return profile_data
            
        except Exception as e:
            logger.error(f"生成用户 {member_id} 画像失败: {e}")
            return None
    
    def _build_profile_data(self, member_id: str, analysis_result, positions):
        """构建用户画像数据"""
        try:
            basic_metrics = analysis_result.basic_metrics
            professional_scores = analysis_result.professional_scores
            
            # 分析期间
            analysis_period_start = min(p['open_time'] for p in positions)
            analysis_period_end = max(p.get('close_time', p['open_time']) for p in positions)
            
            # 计算基础统计
            total_positions = len(positions)
            completed_positions = sum(1 for p in positions if p.get('close_time'))
            total_volume = sum(float(p.get('deal_vol_usdt', 0)) for p in positions)
            
            # 盈亏统计
            profitable_positions = sum(1 for p in positions if float(p.get('profit', 0)) > 0)
            loss_positions = total_positions - profitable_positions
            total_profit = sum(max(0, float(p.get('profit', 0))) for p in positions)
            total_loss = sum(min(0, float(p.get('profit', 0))) for p in positions)
            
            # 构建画像数据
            profile_data = {
                'member_id': member_id,
                'analysis_date': datetime.now().date(),
                'analysis_period_start': analysis_period_start,
                'analysis_period_end': analysis_period_end,
                
                # 基础数据统计
                'total_positions': total_positions,
                'completed_positions': completed_positions,
                'total_volume': total_volume,
                'total_trades': total_positions,
                'avg_trade_size': total_volume / total_positions if total_positions > 0 else 0,
                'total_commission': sum(float(p.get('fee', 0)) for p in positions),
                
                # 盈亏指标
                'profitable_count': profitable_positions,
                'loss_count': loss_positions,
                'total_profit': total_profit,
                'total_loss': abs(total_loss),
                'win_rate': basic_metrics.win_rate,
                'profit_loss_ratio': basic_metrics.profit_loss_ratio,
                'profit_factor': basic_metrics.profit_factor,
                
                # 专业度评分
                'professional_score': professional_scores.total_score,
                'profitability_score': professional_scores.profitability_score,
                'risk_control_score': professional_scores.risk_control_score,
                'trading_behavior_score': professional_scores.trading_behavior_score,
                'market_understanding_score': professional_scores.market_understanding_score,
                
                # 用户分类
                'trader_type': professional_scores.trader_type,
                'confidence_level': professional_scores.confidence_level,
                
                # 资金规模分类
                'fund_scale_category': analysis_result.fund_scale_category,
                'real_trading_volume': total_volume,
                
                # 杠杆分析
                'avg_leverage': basic_metrics.avg_leverage,
                'max_leverage': basic_metrics.max_leverage,
                
                # 异常交易分析
                'abnormal_volume': analysis_result.abnormal_analysis.total_abnormal_volume,
                'wash_trading_volume': analysis_result.abnormal_analysis.wash_trading_volume,
                'high_frequency_volume': analysis_result.abnormal_analysis.high_freq_volume,
                'funding_arbitrage_volume': analysis_result.abnormal_analysis.funding_arbitrage_volume,
                'risk_events_count': analysis_result.abnormal_analysis.risk_events_count,
            }
            
            return profile_data
            
        except Exception as e:
            logger.error(f"构建用户画像数据失败: {e}")
            return None
    
    def _save_user_profile(self, profile_data):
        """保存用户画像到数据库"""
        try:
            # 获取下一个ID
            next_id_result = db_manager.fetch_all("SELECT nextval('user_trading_profiles_id_seq') as next_id")
            next_id = next_id_result[0]['next_id']
            
            # 插入数据
            sql = """
            INSERT INTO user_trading_profiles (
                id, member_id, analysis_date, analysis_period_start, analysis_period_end,
                total_positions, completed_positions, total_volume, total_trades, avg_trade_size, total_commission,
                profitable_count, loss_count, total_profit, total_loss, 
                win_rate, profit_loss_ratio, profit_factor,
                professional_score, profitability_score, risk_control_score, 
                trading_behavior_score, market_understanding_score,
                trader_type, confidence_level, fund_scale_category, real_trading_volume,
                avg_leverage, max_leverage,
                abnormal_volume, wash_trading_volume, high_frequency_volume, 
                funding_arbitrage_volume, risk_events_count
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )
            """
            
            params = [
                next_id,
                profile_data['member_id'],
                profile_data['analysis_date'],
                profile_data['analysis_period_start'],
                profile_data['analysis_period_end'],
                profile_data['total_positions'],
                profile_data['completed_positions'],
                profile_data['total_volume'],
                profile_data['total_trades'],
                profile_data['avg_trade_size'],
                profile_data['total_commission'],
                profile_data['profitable_count'],
                profile_data['loss_count'],
                profile_data['total_profit'],
                profile_data['total_loss'],
                profile_data['win_rate'],
                profile_data['profit_loss_ratio'],
                profile_data['profit_factor'],
                profile_data['professional_score'],
                profile_data['profitability_score'],
                profile_data['risk_control_score'],
                profile_data['trading_behavior_score'],
                profile_data['market_understanding_score'],
                profile_data['trader_type'],
                profile_data['confidence_level'],
                profile_data['fund_scale_category'],
                profile_data['real_trading_volume'],
                profile_data['avg_leverage'],
                profile_data['max_leverage'],
                profile_data['abnormal_volume'],
                profile_data['wash_trading_volume'],
                profile_data['high_frequency_volume'],
                profile_data['funding_arbitrage_volume'],
                profile_data['risk_events_count']
            ]
            
            db_manager.execute_sql(sql, params)
            
        except Exception as e:
            logger.error(f"保存用户画像失败: {e}")
            raise

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='填充用户交易画像数据')
    parser.add_argument('--limit', type=int, help='限制处理的用户数量（用于测试）')
    parser.add_argument('--user-id', type=str, help='只处理指定用户ID')
    
    args = parser.parse_args()
    
    populator = UserTradingProfilesPopulator()
    
    if args.user_id:
        # 处理单个用户
        logger.info(f"为用户 {args.user_id} 生成交易画像")
        profile = populator._generate_user_profile(args.user_id)
        if profile:
            populator._save_user_profile(profile)
            logger.info("✅ 单用户画像生成成功")
        else:
            logger.error("❌ 单用户画像生成失败")
    else:
        # 批量处理
        populator.populate_all_users(limit=args.limit)

if __name__ == '__main__':
    main() 